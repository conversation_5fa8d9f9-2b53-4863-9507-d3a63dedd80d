import { jest } from "@jest/globals";
import { ContinueServerClient } from "../continueServer/stubs/client.js";
import { testConfigHandler, testIde } from "../test/fixtures.js";
import {
  addToTestDir,
  setUpTestDir,
  tearDownTestDir,
  TEST_DIR,
} from "../test/testDir.js";
import { CodebaseIndexer, PauseToken } from "./CodebaseIndexer.js";
import { TestCodebaseIndex } from "./TestCodebaseIndex.js";
import { CodebaseIndex } from "./types.js";

jest.useFakeTimers();

// A subclass of CodebaseIndexer that adds a new CodebaseIndex
class TestCodebaseIndexer extends CodebaseIndexer {
  protected async getIndexesToBuild(): Promise<CodebaseIndex[]> {
    return [new TestCodebaseIndex()];
  }
}

describe("CodebaseIndexer autoIndexThreshold", () => {
  const pauseToken = new PauseToken(false);
  const continueServerClient = new ContinueServerClient(undefined, undefined);
  const codebaseIndexer = new TestCodebaseIndexer(
    testConfigHandler,
    testIde,
    pauseToken,
    continueServerClient,
  );

  beforeAll(async () => {
    tearDownTestDir();
    setUpTestDir();
  });

  afterAll(async () => {
    tearDownTestDir();
  });

  async function refreshIndex() {
    const abortController = new AbortController();
    const abortSignal = abortController.signal;

    const updates = [];
    for await (const update of codebaseIndexer.refreshDirs(
      [TEST_DIR],
      abortSignal,
    )) {
      updates.push(update);
    }
    return updates;
  }

  test("should skip indexing when file count is below threshold", async () => {
    // Mock config with autoIndexThreshold set to 10
    const mockConfig = {
      disableIndexing: false,
      autoIndexThreshold: 10,
      selectedModelByRole: { embed: null },
    };
    
    jest.spyOn(testConfigHandler, 'loadConfig').mockResolvedValue({
      config: mockConfig,
      errors: [],
      configLoadInterrupted: false,
    });

    // Add only 2 files (below threshold of 10)
    addToTestDir([
      ["test1.ts", "console.log('test1');"],
      ["test2.ts", "console.log('test2');"],
    ]);

    const updates = await refreshIndex();
    
    // Should skip indexing and return done status
    const lastUpdate = updates[updates.length - 1];
    expect(lastUpdate.status).toBe("done");
    expect(lastUpdate.desc).toContain("Skipping indexing");
    expect(lastUpdate.desc).toContain("2 files");
    expect(lastUpdate.desc).toContain("threshold: 10");
  });

  test("should proceed with indexing when file count exceeds threshold", async () => {
    // Mock config with autoIndexThreshold set to 1
    const mockConfig = {
      disableIndexing: false,
      autoIndexThreshold: 1,
      selectedModelByRole: { embed: { maxEmbeddingChunkSize: 1000 } },
    };
    
    jest.spyOn(testConfigHandler, 'loadConfig').mockResolvedValue({
      config: mockConfig,
      errors: [],
      configLoadInterrupted: false,
    });

    // Add 2 files (above threshold of 1)
    addToTestDir([
      ["test3.ts", "console.log('test3');"],
      ["test4.ts", "console.log('test4');"],
    ]);

    const updates = await refreshIndex();
    
    // Should proceed with indexing
    const startUpdate = updates.find(update => 
      update.desc?.includes("Starting indexing") && 
      update.desc?.includes("files found")
    );
    expect(startUpdate).toBeDefined();
    expect(startUpdate?.desc).toContain("files found");
  });

  test("should use default threshold of 500 when not configured", async () => {
    // Mock config without autoIndexThreshold
    const mockConfig = {
      disableIndexing: false,
      selectedModelByRole: { embed: null },
    };
    
    jest.spyOn(testConfigHandler, 'loadConfig').mockResolvedValue({
      config: mockConfig,
      errors: [],
      configLoadInterrupted: false,
    });

    // Add only 2 files (below default threshold of 500)
    addToTestDir([
      ["test5.ts", "console.log('test5');"],
      ["test6.ts", "console.log('test6');"],
    ]);

    const updates = await refreshIndex();
    
    // Should skip indexing with default threshold
    const lastUpdate = updates[updates.length - 1];
    expect(lastUpdate.status).toBe("done");
    expect(lastUpdate.desc).toContain("Skipping indexing");
    expect(lastUpdate.desc).toContain("threshold: 500");
  });
});
